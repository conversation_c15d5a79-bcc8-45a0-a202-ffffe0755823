package player

import (
	def "liteframe/internal/common/constant/def"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
	"time"

	"google.golang.org/protobuf/proto"
)

// SeasonBuff 玩家赛季Buff
type SeasonBuff struct {
	p           *Player
	currentBuff *public.PBSeasonBuff // 当前赛季buff数据
	hasCache    bool                 // 是否有缓存数据
	lastUpdate  int64                // 最后更新时间
}

// Init 初始化
func (s *SeasonBuff) Init(p *Player) {
	s.p = p
	s.currentBuff = &public.PBSeasonBuff{}
	s.hasCache = false
	s.lastUpdate = 0
}

// OnLogin 登录时调用
func (s *SeasonBuff) OnLogin() {
	// 登录时请求赛季buff信息
	s.RequestSeasonBuff()
}

// OnLogout 登出时调用
func (s *SeasonBuff) OnLogout() {
	// 登出时的处理逻辑（如果需要）
}

// InitDB 实现Module接口的方法
func (s *SeasonBuff) InitDB(db *dbstruct.UserDB) {
	// 空实现，不需要从数据库加载数据
}

// OnCrossDay 实现Module接口的跨天方法
func (s *SeasonBuff) OnCrossDay(natural bool, nowUnix int64) {
	// 空实现，赛季buff数据不需要在跨天时处理
}

// HasCache 检查是否有有效缓存
func (s *SeasonBuff) HasCache() bool {
	// 如果有缓存且缓存未过期
	if s.hasCache && s.currentBuff != nil {
		// 检查buff是否已过期
		now := time.Now().Unix()
		if s.currentBuff.EndTime > 0 && now < s.currentBuff.EndTime {
			return true
		}
	}
	return false
}

// RequestSeasonBuff 请求赛季buff信息
func (s *SeasonBuff) RequestSeasonBuff(req ...*cs.CLSeasonBuffReq) {
	// 如果有有效缓存，直接返回缓存内容
	if s.HasCache() {
		log.Debug("Using SeasonBuff to cache data",
			log.Kv("uid", s.p.Uid()),
			log.Kv("buffId", s.currentBuff.BuffId),
			log.Kv("endTime", s.currentBuff.EndTime))

		// 构造响应并发送给客户端
		res := &cs.LCSeasonBuffRes{
			ErrorCode: int32(error_code.ERROR_OK),
			Buff:      proto.Clone(s.currentBuff).(*public.PBSeasonBuff),
		}
		s.SeasonBuffRes(res)
		return
	}

	// 构造请求消息
	request := &cs.P2SBSeasonBuffReq{}

	// 发送消息到赛季buff系统
	if err := s.sendToSeasonBuffSystem(request); err != nil {
		log.Error("send to season buff system failed",
			log.Kv("uid", s.p.Uid()),
			log.Err(err))
	}
}

// HandlePlayerSystemSeasonBuffSync 处理从PlayerSystem来的赛季buff同步消息
func (s *SeasonBuff) HandlePlayerSystemSeasonBuffSync(data *cs.PS2PSeasonBuffSync) {
	// 处理从PlayerSystem同步过来的赛季buff数据
	if data.Info == nil {
		log.Error("Handle season buff sync with nil info",
			log.Kv("uid", s.p.Uid()))
		return
	}

	// 更新本地数据
	s.UpdateCache(data.Info)

	// 这种情况下需要主动发给客户端
	res := &cs.LCSeasonBuffRes{
		ErrorCode: int32(error_code.ERROR_OK),
		Buff:      proto.Clone(data.Info).(*public.PBSeasonBuff),
	}
	s.SeasonBuffRes(res)
}

// HandleSeasonBuffResponse 处理来自SeasonBuffSystem的直接响应
func (s *SeasonBuff) HandleSeasonBuffResponse(data *cs.SB2PSeasonBuffRsp) {
	// 处理从系统同步过来的赛季buff数据
	if data.ErrorCode != int32(error_code.ERROR_OK) {
		log.Error("Handle season buff response with error",
			log.Kv("uid", s.p.Uid()),
			log.Kv("errorCode", data.ErrorCode))

		// 即使有错误也需要回复客户端
		res := &cs.LCSeasonBuffRes{
			ErrorCode: data.ErrorCode,
		}
		s.SeasonBuffRes(res)
		return
	}

	// 更新本地数据
	if data.Info != nil {
		s.UpdateCache(data.Info)
	}

	// 转发给客户端
	res := &cs.LCSeasonBuffRes{
		ErrorCode: data.ErrorCode,
		Buff:      proto.Clone(data.Info).(*public.PBSeasonBuff),
	}
	s.SeasonBuffRes(res)
}

// UpdateCache 更新本地缓存数据
func (s *SeasonBuff) UpdateCache(buff *public.PBSeasonBuff) {
	if buff == nil {
		return
	}

	// 存储buff数据
	s.currentBuff = proto.Clone(buff).(*public.PBSeasonBuff)
	s.hasCache = true
	s.lastUpdate = time.Now().Unix()

	log.Debug("Update SeasonBuff cache",
		log.Kv("uid", s.p.Uid()),
		log.Kv("buffId", s.currentBuff.BuffId),
		log.Kv("endTime", s.currentBuff.EndTime))
}

// SeasonBuffRes 向客户端发送赛季buff响应
func (s *SeasonBuff) SeasonBuffRes(res *cs.LCSeasonBuffRes) {
	s.p.RespSeasonBuff(res)
}

// GetCurrentBuff 获取当前赛季buff数据
func (s *SeasonBuff) GetCurrentBuff() *public.PBSeasonBuff {
	return s.currentBuff
}

// sendToSeasonBuffSystem 发送消息到赛季buff系统
func (s *SeasonBuff) sendToSeasonBuffSystem(data proto.Message) error {
	// 附加UID信息到消息中
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Actor_Player2SeasonBuffSystem),
		Data: data,
		Uid:  s.p.Uid(), // 使用Uid字段传递玩家ID
	}

	err := actor.Send(s.p.PID(), actor_def.SeasonBuffSystem, msg)
	if err != nil {
		log.Error("send to season buff system failed",
			log.Kv("from", s.p.PID()),
			log.Kv("to", actor_def.SeasonBuffSystem),
			log.Kv("msgType", proto.MessageName(data)),
			log.Err(err))
	}
	return err
}
