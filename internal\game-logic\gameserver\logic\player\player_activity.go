package player

import (
	"fmt"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/internal/game-logic/gameserver/logic/gameutil"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
	"strconv"
	"time"
)

type Activity struct {
	player *Player
	db     *dbstruct.ActivityData
	// 除了初始化阶段，不能在tick以外的阶段使用
	activityEntityMap *util.ConCurrentMap[int32, *activityEntity] // 活动类型对应的模块
	activityTypeMap   map[public.ActivityType]newActivityEntityTypeFunc
}

type activityEntity struct {
	activityInterface
	timeCfg  *table_data.TableActivityTimeConfig
	timeMsg  *public.PBActivityInfo
	commonDB *dbstruct.ActivityCommonInfo
}

func (a *activityEntity) GetActivityStatus() public.ActivityStatus {
	if a == nil {
		return public.ActivityStatus_ActivityStatus_NO_OPEN
	}

	if a.timeMsg == nil {
		return public.ActivityStatus_ActivityStatus_NO_OPEN
	}

	return a.timeMsg.ActivityStatus
}

func (a *activityEntity) SetActivityStatus(status public.ActivityStatus) {
	a.timeMsg.ActivityStatus = status
	a.commonDB.ActivityStatus = status

	log.Debug(fmt.Sprintf("SetActivityStatus ID:(%v) status:(%v)", a.timeCfg.ID, a.timeMsg.ActivityStatus))
}

func (a *activityEntity) GetActivityStartTime() int64 {
	if a == nil {
		return 0
	}

	if a.timeMsg == nil {
		return 0
	}

	return a.timeMsg.StartTime
}

func (a *activityEntity) GetActivityEndTime() int64 {
	if a == nil {
		return 0
	}

	if a.timeMsg == nil {
		return 0
	}

	return a.timeMsg.EndTime
}

type activityInterface interface {
	OnOpen() error
	OnEnd()
	OnGetReward([]int32) error
	onCrossDay()
	OnMissionComplete([]*MissionInfo)
}

type newActivityEntityTypeFunc func(activityId int32, args ...int64) (activityInterface, error)

func NewActivity(p *Player) *Activity {

	newActivity := &Activity{
		player:            p,
		activityEntityMap: util.NewConCurrentMap[int32, *activityEntity](),
		activityTypeMap:   make(map[public.ActivityType]newActivityEntityTypeFunc),
	}

	newActivity.registerActivityType()

	return newActivity
}

func (m *Activity) registerActivityType() {
	m.activityTypeMap[public.ActivityType_ActivityType_SevenTask] = func(activityId int32, args ...int64) (activityInterface, error) {
		return newActivitySevenTask(m.player, activityId, args...)
	}
}

func (m *Activity) InitDB(db *dbstruct.UserDB) {
	if db.GetGame() == nil {
		db.Game = &dbstruct.GameDB{}
	}

	if db.GetGame().GetActivity() == nil {
		db.GetGame().Activity = &dbstruct.ActivityData{}
	}

	m.db = db.GetGame().GetActivity()

	activityDBMap := make(map[int32]*dbstruct.ActivityCommonInfo)

	for _, activityInfo := range m.db.GetActivityList() {
		if activityInfo == nil {
			continue
		}

		activityDBMap[activityInfo.GetActivityId()] = activityInfo
	}

	// 初始化数据到内存
	for _, activityCfg := range table.GetTable().TableActivityTimeConfig.Data {
		commonInfo, isExist := activityDBMap[activityCfg.ID]

		if isExist && commonInfo.GetActivityStatus() == public.ActivityStatus_ActivityStatus_END {
			continue
		}

		curEntity, err := m.newActivityEntity(activityCfg)

		if err != nil {
			log.Error(fmt.Sprintf("activity id:(%d) isExist:(%v) newActivityEntity error:(%v)", activityCfg.ID, isExist, err))
			continue
		}

		//处理特殊情况
		if isExist {

			curEntity.commonDB = commonInfo

			if curEntity.GetActivityStatus() == public.ActivityStatus_ActivityStatus_END &&
				curEntity.commonDB.GetActivityStatus() == public.ActivityStatus_ActivityStatus_START {
				curEntity.SetActivityStatus(public.ActivityStatus_ActivityStatus_START) //重置下状态，跑一下活动结束
			}
		} else {
			// 玩家没有经历过但是已经过期的活动，就不用放到内存了
			if curEntity.GetActivityStatus() == public.ActivityStatus_ActivityStatus_END {
				continue
			}

			commonInfo = &dbstruct.ActivityCommonInfo{
				ActivityId: activityCfg.ID,
			}

			m.db.ActivityList = append(m.db.ActivityList, commonInfo)
			curEntity.commonDB = commonInfo

			if curEntity.GetActivityStatus() == public.ActivityStatus_ActivityStatus_START {
				curEntity.SetActivityStatus(public.ActivityStatus_ActivityStatus_NO_OPEN) //重置下状态，跑一下活动开始
			} else {
				curEntity.SetActivityStatus(curEntity.GetActivityStatus()) //同步下当时的状态
			}
		}

		m.activityEntityMap.Set(activityCfg.ID, curEntity)
	}
}

func (m *Activity) OnCrossDay(natural bool, nowUnix int64) {
	m.activityEntityMap.IterCb(func(key int32, v *activityEntity) {
		v.onCrossDay()
	})
}

func (m *Activity) OnLogin() {

	msg := &cs.LCPushActivityInfo{}

	m.activityEntityMap.IterCb(func(key int32, v *activityEntity) {
		msg.ActivityInfoList = append(msg.ActivityInfoList, v.timeMsg)
	})

	m.player.SendToClient(rpc_def.LCPushActivityInfo, msg, false)
}

func (m *Activity) Tick() {
	curTime := global.Now().Unix()

	var openList []*activityEntity
	var endList []*activityEntity

	m.activityEntityMap.IterCb(func(key int32, v *activityEntity) {
		if v.GetActivityStatus() == public.ActivityStatus_ActivityStatus_NO_OPEN && v.GetActivityStartTime() < curTime {
			openList = append(openList, v)
		}

		if v.GetActivityStatus() == public.ActivityStatus_ActivityStatus_START && v.GetActivityEndTime() < curTime {
			endList = append(endList, v)
		}
	})

	m.openActivity(openList)
	m.endActivity(endList)
}

// 开启活动
func (m *Activity) openActivity(activityEntities []*activityEntity) {

	if len(activityEntities) == 0 {
		return
	}

	syncMsg := &cs.LCPushActivityInfo{}

	for _, curActivityEntity := range activityEntities {
		if err := curActivityEntity.OnOpen(); err != nil {
			log.Error("activity open error", log.Kv("err", err))
			continue
		}

		curActivityEntity.SetActivityStatus(public.ActivityStatus_ActivityStatus_START)

		syncMsg.ActivityInfoList = append(syncMsg.ActivityInfoList, curActivityEntity.timeMsg)
	}

	m.player.SendToClient(rpc_def.LCPushActivityInfo, syncMsg, false)
}

// 下架活动
func (m *Activity) endActivity(activityEntities []*activityEntity) {

	if len(activityEntities) == 0 {
		return
	}

	syncMsg := &cs.LCPushActivityInfo{}

	for _, curActivityEntity := range activityEntities {
		curActivityEntity.OnEnd()
		curActivityEntity.SetActivityStatus(public.ActivityStatus_ActivityStatus_END)

		m.activityEntityMap.Remove(curActivityEntity.timeCfg.ID)
		syncMsg.ActivityInfoList = append(syncMsg.ActivityInfoList, curActivityEntity.timeMsg)
	}

	m.player.SendToClient(rpc_def.LCPushActivityInfo, syncMsg, false)
}

func (m *Activity) newActivityEntity(timeCfg *table_data.TableActivityTimeConfig) (*activityEntity, error) {

	if timeCfg == nil {
		return nil, fmt.Errorf("activity time config is nil")
	}

	timeMsg := m.getPbActivityInfo(timeCfg)

	newFunc, isExist := m.activityTypeMap[public.ActivityType(timeCfg.Type)]

	if !isExist {
		return nil, fmt.Errorf("activity type:(%d) not exist", timeCfg.Type)
	}

	curEntity, err := newFunc(timeCfg.ID, timeMsg.StartTime)

	if err != nil {
		return nil, err
	}

	e := &activityEntity{
		activityInterface: curEntity,
		timeMsg:           timeMsg,
		timeCfg:           timeCfg,
	}

	return e, nil
}

func (m *Activity) getPbActivityInfo(timeCfg *table_data.TableActivityTimeConfig) *public.PBActivityInfo {

	if timeCfg == nil {
		return nil
	}

	activityInfo := &public.PBActivityInfo{
		CurActivityId: timeCfg.ID,
	}

	//0.以开服时间日零点为基准
	//1.以绝对时间为基准
	//2.以玩家创建角色时间为基准
	if timeCfg.TimeCalcType == 0 || timeCfg.TimeCalcType == 2 {

		cfgStartTime, err := strconv.ParseInt(timeCfg.StartTime, 10, 64)

		if err != nil {
			log.Error(fmt.Sprintf("activity id:(%d) TimeCalcType:(%d) StartTime:(%s) strconv.ParseInt error:(%v)",
				timeCfg.ID, timeCfg.TimeCalcType, timeCfg.StartTime, err))
			return activityInfo
		}

		cfgEndTime, err := strconv.ParseInt(timeCfg.EndTime, 10, 64)

		if err != nil {
			log.Error(fmt.Sprintf("activity id:(%d) TimeCalcType:(%d) EndTime:(%s) strconv.ParseInt error:(%v)",
				timeCfg.ID, timeCfg.TimeCalcType, timeCfg.EndTime, err))
			return activityInfo
		}

		baseTime := gameutil.DayZeroTime(global.OpenServerTime)

		if timeCfg.TimeCalcType == 2 {
			baseTime = m.player.GetUserDB().GetBase().GetCreateTime()
		}

		// 分钟数
		activityInfo.StartTime = baseTime + cfgStartTime*60
		activityInfo.EndTime = baseTime + cfgEndTime*60
	}

	if timeCfg.TimeCalcType == 1 {

		cfgStartTime, err := time.Parse(game_def.ConfigTimeFormat, timeCfg.StartTime)

		if err != nil {
			log.Error(fmt.Sprintf("activity id:(%d) TimeCalcType:(%d) StartTime:(%s) time.Parse error:(%v)",
				timeCfg.ID, timeCfg.TimeCalcType, timeCfg.StartTime, err))
			return activityInfo
		}

		cfgEndTime, err := time.Parse(game_def.ConfigTimeFormat, timeCfg.EndTime)

		if err != nil {
			log.Error(fmt.Sprintf("activity id:(%d) TimeCalcType:(%d) EndTime:(%s) time.Parse error:(%v)",
				timeCfg.ID, timeCfg.TimeCalcType, timeCfg.EndTime, err))
			return activityInfo
		}

		activityInfo.StartTime = cfgStartTime.Unix()
		activityInfo.EndTime = cfgEndTime.Unix()
	}

	curTime := global.Now().Unix()

	if activityInfo.EndTime < curTime {
		activityInfo.ActivityStatus = public.ActivityStatus_ActivityStatus_END
	} else if activityInfo.StartTime > curTime {
		activityInfo.ActivityStatus = public.ActivityStatus_ActivityStatus_NO_OPEN
	} else {
		activityInfo.ActivityStatus = public.ActivityStatus_ActivityStatus_START
	}

	return activityInfo
}

func (m *Activity) OnMissionComplete(missionInfo []*MissionInfo) {
	m.activityEntityMap.IterCb(func(key int32, v *activityEntity) {
		v.OnMissionComplete(missionInfo)
	})
}

// GetReward 领取奖励
// @Export
func (m *Activity) GetReward(id int32, param []int32) error {

	if curActivityEntity, ok := m.activityEntityMap.Get(id); ok {
		if curActivityEntity.GetActivityStatus() != public.ActivityStatus_ActivityStatus_START {
			return fmt.Errorf("activity id:(%d) status:(%d) not start", id, curActivityEntity.GetActivityStatus())
		}

		if err := curActivityEntity.OnGetReward(param); err != nil {
			return fmt.Errorf("activity id:(%d) OnGetReward error:(%v)", id, err)
		}
	}

	return fmt.Errorf("activity id:(%d) not exist", id)
}

func (m *Activity) GetSevenDayActivityData(activityId int32) *cs.LCGetSevenDayActivityData {
	msg := &cs.LCGetSevenDayActivityData{}

	// 允许不进行赋值
	m.activityEntityMap.IterCb(func(key int32, v *activityEntity) {

		// 已经获取到了
		if msg.GetActivityId() != 0 {
			return
		}

		if v.GetActivityStatus() != public.ActivityStatus_ActivityStatus_START || v.timeCfg.Type != int32(public.ActivityType_ActivityType_SevenTask) {
			return
		}

		if activityId != 0 && activityId != v.timeCfg.ID {
			return
		}

		msg.ActivityId = v.timeCfg.ID
		msg.UnlockDay = int32(gameutil.GetDiffDay(v.GetActivityStartTime(), global.Now().Unix()))
		msg.EndTime = v.GetActivityEndTime()
	})

	return msg
}
