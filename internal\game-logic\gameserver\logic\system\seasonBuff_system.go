package system

import (
	"context"
	"fmt"
	def "liteframe/internal/common/constant/def"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/logic/system/dao/dbhelper"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
	"time"

	"google.golang.org/protobuf/proto"
)

// SeasonBuffSystem 赛季buff系统
type SeasonBuffSystem struct {
	dispatch   *actor.Dispatcher
	seasonBuff *dbstruct.SeasonBuffDB
}

func (sb *SeasonBuffSystem) OnStop() {
}

// NewSeasonBuffSystem 创建一个新的赛季buff系统
func NewSeasonBuffSystem() *SeasonBuffSystem {
	system := &SeasonBuffSystem{
		dispatch: actor.NewDispatcher(100, func(uid uint64, id uint32, ms int64) {
			if ms > 100 {
				log.Debug("SeasonBuff system", log.Kv("id", id), log.Kv("cost", ms))
			}
		}),
		seasonBuff: &dbstruct.SeasonBuffDB{
			Data: &public.PBSeasonBuff{},
		},
	}
	system.registerHandler()
	return system
}

// PID 获取赛季buff系统的PID
func (sb *SeasonBuffSystem) PID() actor.PID {
	return actor_def.SeasonBuffSystem
}

// Process 处理消息
func (sb *SeasonBuffSystem) Process(msg *actor.Message) {
	if err := sb.dispatch.Dispatch(context.Background(), msg); err != nil {
		log.Error("season buff system dispatch failed", log.Err(err), log.Kv("msgId", msg.Id))
	}
}

// registerHandler 注册消息处理器
func (sb *SeasonBuffSystem) registerHandler() {
	sb.dispatch.Register(uint32(def.MsgId_Actor_Player2SeasonBuffSystem), sb.playerMessageHandler)
	sb.dispatch.Register(uint32(def.MsgId_Actor_Second), sb.serverTickHandler)
}

// LoadSeasonBuffSync 从数据库同步加载赛季buff数据
func (sb *SeasonBuffSystem) LoadSeasonBuffSync() error {
	dbData, err := dbhelper.LoadSeasonBuffDB(actor_def.SystemPID)
	if err != nil {
		// 如果没有数据，初始化空数据
		sb.seasonBuff = &dbstruct.SeasonBuffDB{
			Data: &public.PBSeasonBuff{},
		}
		sb.initSeasonBuff()
		log.Error("load seasonBuff db failed", log.Err(err))
		return err
	}

	// 反序列化并复制数据
	sb.seasonBuff = &dbstruct.SeasonBuffDB{}
	if err := proto.Unmarshal(dbData, sb.seasonBuff); err != nil {
		log.Error("unmarshal seasonBuff data failed", log.Err(err))
		return err
	}

	if sb.seasonBuff.Data == nil {
		sb.seasonBuff.Data = &public.PBSeasonBuff{}
	}

	//sb.TestAddSeasonBuff(101, 100)
	sb.TestClearSeasonBuff()

	log.Info("SeasonBuff data loaded successfully")
	return nil
}

// SaveSeasonBuffData 保存赛季buff数据到数据库
func (sb *SeasonBuffSystem) SaveSeasonBuffData() {
	if sb.seasonBuff == nil || sb.seasonBuff.Data == nil {
		log.Error("seasonBuff data is nil, cannot save")
		return
	}

	log.Info("Saving season buff data",
		log.Kv("buffId", sb.seasonBuff.Data.BuffId),
		log.Kv("endTime", sb.seasonBuff.Data.EndTime))

	// 使用自己的PID作为发送者，而不是SystemPID
	dbhelper.SaveSeasonBuffDB(sb.PID(), sb.seasonBuff, func(err error) {
		if err != nil {
			log.Error("save seasonBuff db failed", log.Err(err))
			return
		}
		log.Info("SeasonBuff data saved successfully")
	})
}

// initSeasonBuff 初始化赛季buff数据
func (sb *SeasonBuffSystem) initSeasonBuff() {
	// 初始化赛季buff数据
	sb.seasonBuff.Data.BuffId = 0  // 默认无buff
	sb.seasonBuff.Data.EndTime = 0 // 默认无结束时间
}

// playerMessageHandler 处理来自玩家的消息
func (sb *SeasonBuffSystem) playerMessageHandler(ctx context.Context, msg *actor.Message) error {
	uid := msg.Uid // 从msg中获取UID

	switch data := msg.Data.(type) {
	case *cs.CLSeasonBuffReq:
		return sb.handleSeasonBuffRequest(ctx, uid, data)
	default:
		log.Warn("seasonBuff system: unknown player message",
			log.Kv("msgId", msg.Id),
			log.Kv("uid", uid),
			log.Kv("dataType", fmt.Sprintf("%T", msg.Data)))
	}
	return nil
}

// serverTickHandler 服务器定时任务处理
func (sb *SeasonBuffSystem) serverTickHandler(ctx context.Context, msg *actor.Message) error {
	// 检查是否需要刷新活动数据
	newActivity := sb.getActiveActivityConfig()

	// 没有活动配置，直接返回
	if newActivity == nil {
		return nil
	}

	// 记录当前buff状态
	oldBuffId := sb.seasonBuff.Data.BuffId
	oldEndTime := sb.seasonBuff.Data.EndTime

	// 根据活动配置更新buff状态
	updatedBuff := false

	// 检查BuffId是否需要更新
	if sb.seasonBuff.Data.BuffId != newActivity.NextId {
		sb.seasonBuff.Data.BuffId = newActivity.NextId
		updatedBuff = true
	}

	// 检查结束时间是否需要更新
	endTime, err := time.Parse("2006-01-02 15:04:05", newActivity.EndTime)
	var newEndTime int64
	if err == nil {
		newEndTime = endTime.Unix()
	} else {
		// 如果解析失败，设置为一周后
		newEndTime = time.Now().AddDate(0, 0, 7).Unix()
		log.Warn("parse activity end time failed, use default",
			log.Kv("activity_id", newActivity.ID),
			log.Kv("end_time", newActivity.EndTime),
			log.Err(err))
	}

	if sb.seasonBuff.Data.EndTime != newEndTime {
		sb.seasonBuff.Data.EndTime = newEndTime
		updatedBuff = true
	}

	// 只有当buff数据实际变化时，才通知玩家并保存数据
	if updatedBuff {
		log.Info("Season buff updated",
			log.Kv("old_buff_id", oldBuffId),
			log.Kv("new_buff_id", sb.seasonBuff.Data.BuffId),
			log.Kv("old_end_time", oldEndTime),
			log.Kv("new_end_time", sb.seasonBuff.Data.EndTime))

		// 通知所有玩家
		sb.notifyAllPlayers()

		// 保存到数据库
		sb.SaveSeasonBuffData()
	}

	return nil
}

// getActiveActivityConfig 获取当前活动配置
func (sb *SeasonBuffSystem) getActiveActivityConfig() *table_data.TableActivityTimeConfig {
	// 获取活动时间配置表
	activityTable := table.GetTable().TableActivityTimeConfig
	if activityTable == nil {
		log.Error("TableActivityTimeConfig is nil")
		return nil
	}

	// 找出当前时间有效的类型为4的活动
	currentTime := time.Now()
	for _, activity := range activityTable.Data {
		// 只处理类型为4的活动（赛季Buff）
		if activity.Type != 4 {
			continue
		}

		// 根据时间计算类型检查活动是否有效
		var startTime, endTime time.Time
		var err error

		// 解析开始时间和结束时间
		// 简单处理，假设时间格式为"2006-01-02 15:04:05"
		startTime, err = time.Parse("2006-01-02 15:04:05", activity.StartTime)
		if err != nil {
			log.Error("parse start time failed",
				log.Kv("activity_id", activity.ID),
				log.Kv("start_time", activity.StartTime),
				log.Err(err))
			continue
		}

		endTime, err = time.Parse("2006-01-02 15:04:05", activity.EndTime)
		if err != nil {
			log.Error("parse end time failed",
				log.Kv("activity_id", activity.ID),
				log.Kv("end_time", activity.EndTime),
				log.Err(err))
			continue
		}

		// 检查当前时间是否在活动时间范围内
		if currentTime.After(startTime) && currentTime.Before(endTime) {
			return activity
		}
	}

	return nil
}

// notifyAllPlayers 通知所有在线玩家
func (sb *SeasonBuffSystem) notifyAllPlayers() {
	// 构造发送给PlayerSystem的同步消息
	syncMsg := &cs.SB2PSSeasonBuffSync{}

	// 如果有有效的buff数据，添加到同步消息中
	if sb.seasonBuff != nil && sb.seasonBuff.Data != nil {
		syncMsg.Info = proto.Clone(sb.seasonBuff.Data).(*public.PBSeasonBuff)
	}

	// 发送给所有在线玩家(nil表示广播)
	err := sb.sendToPlayerSystem(syncMsg, nil)
	if err != nil {
		log.Error("send to player system failed", log.Err(err))
	}
}

// handleSeasonBuffRequest 处理来自玩家的赛季buff请求
func (sb *SeasonBuffSystem) handleSeasonBuffRequest(ctx context.Context, uid uint64, req *cs.CLSeasonBuffReq) error {
	log.Info("handleSeasonBuffRequest", log.Kv("uid", uid))

	// 检查玩家的赛季buff状态
	// 构造直接发送给Player的响应数据
	res := &cs.SB2PSeasonBuffRsp{
		ErrorCode: int32(error_code.ERROR_OK),
	}

	// 如果当前有赛季buff，返回buff信息
	if sb.seasonBuff != nil && sb.seasonBuff.Data != nil {
		res.Info = proto.Clone(sb.seasonBuff.Data).(*public.PBSeasonBuff)
	}

	// 发送到玩家
	err := sb.sendToPlayerByUID(uid, res)
	if err != nil {
		log.Error("send to player system failed", log.Err(err))
	}
	return nil
}

// sendToPlayerSystem 发送消息到玩家系统
func (sb *SeasonBuffSystem) sendToPlayerSystem(data proto.Message, uids []uint64) error {
	// 直接使用传入的SB2PSSeasonBuffSync消息
	syncMsg, ok := data.(*cs.SB2PSSeasonBuffSync)
	if !ok {
		log.Error("sendToPlayerSystem: invalid message type, expect *cs.SB2PSSeasonBuffSync",
			log.Kv("actual", fmt.Sprintf("%T", data)))
		return fmt.Errorf("invalid message type: %T", data)
	}

	// 检查消息内容
	if syncMsg.Info == nil && sb.seasonBuff != nil && sb.seasonBuff.Data != nil {
		// 当传入的消息无Info时，自动补充当前Buff数据
		syncMsg.Info = proto.Clone(sb.seasonBuff.Data).(*public.PBSeasonBuff)
	}

	// 直接发送同步消息到PlayerSystem
	err := actor.Send(sb.PID(), actor_def.PlayerSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_SeasonBuffSystem2PlayerSystem),
		Data: syncMsg,
	})
	if err != nil {
		uidsDesc := "all"
		if uids != nil && len(uids) > 0 {
			uidsDesc = fmt.Sprintf("%v", uids)
		}

		log.Error("send to player system failed",
			log.Kv("from", sb.PID()),
			log.Kv("to", actor_def.PlayerSystemPID),
			log.Kv("msgType", proto.MessageName(syncMsg)),
			log.Kv("uids", uidsDesc),
			log.Err(err))
	}
	return err
}

// sendToPlayerByUID 通过UID发送消息到玩家Actor
func (sb *SeasonBuffSystem) sendToPlayerByUID(uid uint64, msg proto.Message) error {
	// 直接相应消息，使用player的uid作为参数
	err := actor.Send(sb.PID(), actor_def.PlayerSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_SeasonBuffSystem2Player),
		Uid:  uid, // 使用Uid字段指定目标玩家
		Data: msg,
	})

	if err != nil {
		log.Error("sendToPlayerByUID failed",
			log.Kv("from", sb.PID()),
			log.Kv("uid", uid),
			log.Kv("msgType", proto.MessageName(msg)),
			log.Err(err))
	}
	return err
}

// sendToPlayer 直接发送消息到玩家Actor
func (sb *SeasonBuffSystem) sendToPlayer(to actor.PID, msg proto.Message) error {
	err := actor.Send(sb.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_SeasonBuffSystem2Player),
		Data: msg,
	})
	if err != nil {
		log.Error("send to player failed",
			log.Kv("from", sb.PID()),
			log.Kv("to", to),
			log.Kv("msgType", proto.MessageName(msg)),
			log.Err(err))
	}
	return err
}

// TestAddSeasonBuff 测试函数：写入测试数据并推送给玩家
// 函数接收buffId和持续时间（小时）作为参数
// 用法：通过GM工具或其他方式直接调用此函数测试数据库写入及推送功能
func (sb *SeasonBuffSystem) TestAddSeasonBuff(buffId int32, durationHours int) {
	// 记录原始数据用于日志
	origBuffId := uint32(0)
	origEndTime := int64(0)
	if sb.seasonBuff != nil && sb.seasonBuff.Data != nil {
		origBuffId = uint32(sb.seasonBuff.Data.BuffId)
		origEndTime = sb.seasonBuff.Data.EndTime
	}

	// 设置测试数据
	if sb.seasonBuff == nil {
		sb.seasonBuff = &dbstruct.SeasonBuffDB{
			Data: &public.PBSeasonBuff{},
		}
	}

	// 设置BuffId
	sb.seasonBuff.Data.BuffId = buffId

	// 设置过期时间为当前时间加上指定的小时数
	sb.seasonBuff.Data.EndTime = time.Now().Add(time.Duration(durationHours) * time.Hour).Unix()

	log.Info("TestAddSeasonBuff: successfully",
		log.Kv("old_buff_id", origBuffId),
		log.Kv("new_buff_id", sb.seasonBuff.Data.BuffId),
		log.Kv("old_end_time", origEndTime),
		log.Kv("new_end_time", sb.seasonBuff.Data.EndTime),
		log.Kv("duration_hours", durationHours))

	// 保存到数据库
	sb.SaveSeasonBuffData()

	// 通知所有玩家
	sb.notifyAllPlayers()
}

// TestClearSeasonBuff 测试函数：清除测试数据并推送给玩家
// 用法：通过GM工具或其他方式直接调用此函数测试数据库清除及推送功能
func (sb *SeasonBuffSystem) TestClearSeasonBuff() {
	// 记录原始数据用于日志
	origBuffId := int32(0)
	origEndTime := int64(0)
	if sb.seasonBuff != nil && sb.seasonBuff.Data != nil {
		origBuffId = sb.seasonBuff.Data.BuffId
		origEndTime = sb.seasonBuff.Data.EndTime
	}

	// 清除数据
	if sb.seasonBuff == nil {
		sb.seasonBuff = &dbstruct.SeasonBuffDB{
			Data: &public.PBSeasonBuff{},
		}
	}

	// 重置BuffId和过期时间
	sb.seasonBuff.Data.BuffId = 0
	sb.seasonBuff.Data.EndTime = 0

	log.Info("TestClearSeasonBuff successfully",
		log.Kv("old_buff_id", origBuffId),
		log.Kv("old_end_time", origEndTime))

	// 保存到数据库
	sb.SaveSeasonBuffData()

	// 通知所有玩家
	sb.notifyAllPlayers()
}
